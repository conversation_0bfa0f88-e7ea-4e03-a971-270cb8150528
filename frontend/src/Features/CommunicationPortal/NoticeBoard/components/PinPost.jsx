import { useDispatch } from 'react-redux';
import { FaThumbtack } from 'react-icons/fa';
import { togglePinNotice } from '../../../../redux/slices/api/noticeApi';

/**
 * Custom hook for pin/unpin functionality
 */
const usePinPost = () => {
  const dispatch = useDispatch();

  const pinPost = async (noticeId) => {
    try {
      await dispatch(togglePinNotice(noticeId)).unwrap();
      return { success: true };
    } catch (error) {
      console.error('Error pinning notice:', error);
      return { success: false, error };
    }
  };

  const unpinPost = async (noticeId) => {
    try {
      await dispatch(togglePinNotice(noticeId)).unwrap();
      return { success: true };
    } catch (error) {
      console.error('Error unpinning notice:', error);
      return { success: false, error };
    }
  };

  const togglePin = async (noticeId) => {
    try {
      await dispatch(togglePinNotice(noticeId)).unwrap();
      return { success: true };
    } catch (error) {
      console.error('Error toggling pin status:', error);
      return { success: false, error };
    }
  };

  return {
    pinPost,
    unpinPost,
    togglePin
  };
};

/**
 * PinIcon Component
 * Renders the pin icon for pinned notices with click functionality
 */
export const PinIcon = ({ notice, onPinIconClick, currentTab, isPinned, onClick, className = "" }) => {
  // Support both new interface (notice, onPinIconClick) and legacy interface (isPinned, onClick)
  const pinned = notice ? (notice.is_pinned || notice.pinned || notice.isPinned) : isPinned;
  if (!pinned) return null;

  // Don't show pin icons in expired tab (tab 3)
  if (currentTab === 3) return null;

  const handleClick = (event) => {
    event.stopPropagation();
    if (notice && onPinIconClick) {
      onPinIconClick(notice.id, event);
    } else if (onClick) {
      onClick(event);
    }
  };

  return (
    <FaThumbtack
      className={`w-[16px] h-[16px] text-primary transform rotate-45 cursor-pointer hover:text-primaryDark transition-colors ${className}`}
      title="Click to move this pinned post to expired"
      onClick={handleClick}
    />
  );
};

export default usePinPost;
