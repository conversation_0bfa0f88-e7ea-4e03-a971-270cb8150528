import React, { useState, useEffect, useRef, useCallback } from "react";
import { FaFlag } from "react-icons/fa";
import { FaUserGroup } from "react-icons/fa6";
import { HiDotsHorizontal } from "react-icons/hi";
import { HiUserCircle } from "react-icons/hi";
import AnnouncementActionMenu from "../components/AnnouncementActionMenu";
import { PinIcon } from "../components/PinPost";
import UserCountDisplay from "../components/UserCountDisplay";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";

// Priority color mapping - consistent with PriorityDropdown component
const PRIORITY_COLORS = {
  urgent: "text-red-500",
  high: "text-yellow-500",
  normal: "text-primary",
  low: "text-gray-400"
};

const getPriorityColor = (priority) => {
  return PRIORITY_COLORS[priority?.toLowerCase()] || "text-gray-500";
};

/**
 * AnnouncementListPreview Component
 * Renders the list of announcements with their preview cards in a row-wise masonry layout
 */
const AnnouncementListPreview = ({
  // Data props
  announcements,
  loading,

  // UI state props
  openDropdownId,
  dropdownRef,
  currentTab,

  // Handlers
  handleDropdownToggle,
  handleEditAnnouncement,
  handleAnnouncementHistory,
  handleMoveToExpired,
  handleReminder,
  handlePinPost,
  handlePinIconClick,
  handleDirectCommunication,
  handleDeleteAnnouncement,
  handleRestoreAnnouncement,
  handleImageClick,
  handleDocumentClick,

  // Helper functions
  isDocument,
  getFileIcon
}) => {
  const containerRef = useRef(null);
  const [positions, setPositions] = useState([]);
  const [containerHeight, setContainerHeight] = useState(0);
  const [isLayoutReady, setIsLayoutReady] = useState(false);
  const [isCalculating, setIsCalculating] = useState(false);
  const layoutTimeoutRef = useRef(null);
  const resizeObserverRef = useRef(null);

  // Debounced layout calculation to prevent excessive recalculations
  const calculateLayout = useCallback(() => {
    if (!containerRef.current || announcements.length === 0 || isCalculating) {
      return;
    }

    setIsCalculating(true);

    const containerWidth = containerRef.current.offsetWidth;
    const cardWidth = 350; // Fixed card width
    const gap = 8; // 8px gap between cards

    // Calculate number of columns that fit
    const columns = Math.max(
      1,
      Math.floor((containerWidth + gap) / (cardWidth + gap))
    );
    const columnHeights = new Array(columns).fill(0);
    const newPositions = [];

    // Get all card elements to measure their actual heights
    const cardElements = containerRef.current.querySelectorAll("[data-card-id]");

    if (cardElements.length === announcements.length) {
      // All cards are rendered, measure their heights
      announcements.forEach((_, index) => {
        const cardElement = cardElements[index];
        const cardHeight = cardElement ? cardElement.offsetHeight : 300;

        // Find the shortest column for optimal space usage
        const shortestColumn = columnHeights.indexOf(
          Math.min(...columnHeights)
        );

        // Position the card
        newPositions.push({
          left: shortestColumn * (cardWidth + gap),
          top: columnHeights[shortestColumn]
        });

        // Update column height with actual card height + gap
        columnHeights[shortestColumn] += cardHeight + gap;
      });

      setPositions(newPositions);
      setContainerHeight(Math.max(...columnHeights) - gap);
      setIsLayoutReady(true);
      setIsCalculating(false);
    } else {
      // Cards not fully rendered yet, wait for next frame
      setIsCalculating(false);
      requestAnimationFrame(() => {
        if (!isCalculating) {
          calculateLayout();
        }
      });
    }
  }, [announcements, isCalculating]);

  // Layout effect with improved stability
  useEffect(() => {
    if (!containerRef.current || announcements.length === 0) {
      setPositions([]);
      setContainerHeight(0);
      setIsLayoutReady(false);
      return;
    }

    // Clear any existing timeout
    if (layoutTimeoutRef.current) {
      clearTimeout(layoutTimeoutRef.current);
    }

    // Debounce layout calculation to prevent rapid recalculations
    layoutTimeoutRef.current = setTimeout(() => {
      calculateLayout();
    }, 100);

    return () => {
      if (layoutTimeoutRef.current) {
        clearTimeout(layoutTimeoutRef.current);
      }
    };
  }, [announcements, calculateLayout]);

  // Resize observer with debouncing
  useEffect(() => {
    if (!containerRef.current) return;

    // Disconnect existing observer
    if (resizeObserverRef.current) {
      resizeObserverRef.current.disconnect();
    }

    let resizeTimeout;
    resizeObserverRef.current = new ResizeObserver(() => {
      // Debounce resize events to prevent excessive recalculations
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        if (!isCalculating) {
          calculateLayout();
        }
      }, 150);
    });

    resizeObserverRef.current.observe(containerRef.current);

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
      clearTimeout(resizeTimeout);
    };
  }, [calculateLayout, isCalculating]);

  if (loading) {
    return (
      <div className="col-span-full flex justify-center items-center py-12">
        <LoadingAnimation />
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="relative w-full"
      style={{
        height: isLayoutReady ? `${containerHeight}px` : "auto",
        minHeight: announcements.length > 0 ? "300px" : "auto"
      }}
    >
      {announcements.map((announcement, index) => {
        const position = positions[index];
        const hasPosition = isLayoutReady && position;

        return (
          <div
            key={announcement.id}
            data-card-id={announcement.id}
            className={`bg-white border border-primary rounded-lg p-4 shadow-sm transition-all duration-300 ease-out ${
              hasPosition ? "absolute" : "relative mb-2"
            }`}
            style={{
              width: "350px",
              opacity: isCalculating ? 0.7 : 1,
              transform: isCalculating ? "scale(0.98)" : "scale(1)",
              ...(hasPosition
                ? {
                    left: `${position.left}px`,
                    top: `${position.top}px`
                  }
                : {
                    visibility: isLayoutReady ? "hidden" : "visible"
                  })
            }}
          >
            {/* Header */}
            <div className="flex justify-between items-start mb-3">
              <div className="flex items-center space-x-2">
                <div className="w-[24px] h-[24px] rounded-full flex items-center justify-center">
                  {announcement.postAs === "creator" ? (
                    <HiUserCircle className="w-8 h-8" color="gray" />
                  ) : announcement.postAs === "group" ? (
                    <FaUserGroup className="w-8 h-8" color="gray" />
                  ) : announcement.postAs === "member" ? (
                    <FaUserGroup className="w-8 h-8" color="gray" />
                  ) : (
                    <FaUserGroup className="w-8 h-8" color="gray" />
                  )}
                </div>
                <div>
                  <h3 className="text-black text-[14px] font-bold">
                    {announcement.author}
                  </h3>
                  <p className="text-black text-[11px] font-bold">
                    Creator {announcement.creatorName}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {/* Pin Icon */}
                <PinIcon
                  announcement={announcement}
                  onPinIconClick={handlePinIconClick}
                  currentTab={currentTab}
                />
                {/* Priority Flag */}
                {announcement.priority && (
                  <FaFlag
                    className={`w-[16px] h-[16px] ${getPriorityColor(
                      announcement.priority
                    )}`}
                  />
                )}
                <UserCountDisplay announcement={announcement} />
                <div
                  className="relative"
                  ref={openDropdownId === announcement.id ? dropdownRef : null}
                >
                  <HiDotsHorizontal
                    className="w-[16px] h-[16px] text-primary cursor-pointer hover:text-[#2A7A78]"
                    onClick={() => handleDropdownToggle(announcement.id)}
                  />
                  {openDropdownId === announcement.id && (
                    <AnnouncementActionMenu
                      announcement={announcement}
                      onEdit={handleEditAnnouncement}
                      onHistory={handleAnnouncementHistory}
                      onMoveToExpired={handleMoveToExpired}
                      onReminder={handleReminder}
                      onPinPost={handlePinPost}
                      onDirectCommunication={handleDirectCommunication}
                      onDelete={handleDeleteAnnouncement}
                      onRestore={handleRestoreAnnouncement}
                      onClose={() => handleDropdownToggle(null)}
                    />
                  )}
                </div>
              </div>
            </div>

            {/* Date */}
            <div className="text-[11px] mb-2 flex items-center gap-4">
              <span className="text-primary font-bold whitespace-nowrap">
                Start:{" "}
                {(() => {
                  const date = new Date(announcement.startDate);
                  const day = date.getDate().toString().padStart(2, "0");
                  const month = (date.getMonth() + 1)
                    .toString()
                    .padStart(2, "0");
                  const year = date.getFullYear();
                  const time = announcement.startTime || "";
                  const [hours, minutes] = time.split(":");
                  const hour24 = parseInt(hours);
                  const hour12 =
                    hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
                  const period = hour24 >= 12 ? "pm" : "am";
                  return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
                })()}
              </span>
              <span className="text-error font-bold whitespace-nowrap">
                Expire:{" "}
                {(() => {
                  const date = new Date(announcement.endDate);
                  const day = date.getDate().toString().padStart(2, "0");
                  const month = (date.getMonth() + 1)
                    .toString()
                    .padStart(2, "0");
                  const year = date.getFullYear();
                  const time = announcement.endTime || "";
                  const [hours, minutes] = time.split(":");
                  const hour24 = parseInt(hours);
                  const hour12 =
                    hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
                  const period = hour24 >= 12 ? "pm" : "am";
                  return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
                })()}
              </span>
            </div>

            {/* Label - Next line */}
            {announcement.label && (
              <div className="text-[12px] mb-2">
                <div className="flex flex-wrap gap-1">
                  {announcement.label.split(",").map((label, index) => (
                    <span
                      key={index}
                      className="bg-[#F5F5F5] text-black text-[10px] px-2 py-1 rounded font-bold"
                    >
                      {label.trim()}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Title */}
            <h4 className="text-[#000] text-[14px] font-semibold mb-2 line-clamp-2">
              {announcement.title}
            </h4>

            {/* Description */}
            <p className="text-[#666] text-[12px] mb-3">
              {announcement.description}
            </p>

            {/* Attachments - Show only if they exist */}
            {announcement.attachments &&
              announcement.attachments.length > 0 && (
                <div className="mb-3">
                  <div className="space-y-2">
                    {(() => {
                      console.log(
                        `Announcement ${announcement.id} attachments:`,
                        announcement.attachments
                      );
                      console.log(
                        `Attachment structure for announcement ${announcement.id}:`,
                        announcement.attachments?.map((att) => ({
                          id: att.id,
                          file_url: att.file_url,
                          file_name: att.file_name,
                          url: att.url,
                          name: att.name
                        }))
                      );

                      const totalAttachments = announcement.attachments.length;

                      return (
                        <div className="space-y-2">
                          {/* Main/First Image/PDF - Display prominently */}
                          <div
                            key={announcement.attachments[0].id || 0}
                            className="relative bg-gray-100 overflow-hidden cursor-pointer hover:opacity-90 transition-all duration-200 border border-gray-200 w-[316px] h-[243px] rounded-[16px]"
                            onClick={() =>
                              isDocument(
                                announcement.attachments[0].file_name ||
                                  announcement.attachments[0].name
                              )
                                ? handleDocumentClick(
                                    announcement.attachments[0]
                                  )
                                : handleImageClick(
                                    announcement.attachments[0],
                                    announcement
                                  )
                            }
                          >
                            {isDocument(
                              announcement.attachments[0].file_name ||
                                announcement.attachments[0].name
                            ) ? (
                              <div className="w-full h-full flex items-center justify-center">
                                <div className="flex items-center">
                                  {getFileIcon(
                                    announcement.attachments[0].file_name ||
                                      announcement.attachments[0].name
                                  )}
                                  <div className="ml-2 text-left">
                                    <div className="text-sm font-medium text-gray-900">
                                      {(
                                        announcement.attachments[0].file_name ||
                                        announcement.attachments[0].name
                                      )
                                        ?.toLowerCase()
                                        .endsWith(".pdf")
                                        ? "PDF Document"
                                        : "Word Document"}
                                    </div>
                                    <div className="text-xs text-gray-500">
                                      {announcement.attachments[0].file_name ||
                                        announcement.attachments[0].name}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <img
                                src={
                                  announcement.attachments[0].file_url ||
                                  announcement.attachments[0].url ||
                                  announcement.attachments[0]
                                }
                                alt={
                                  announcement.attachments[0].file_name ||
                                  announcement.attachments[0].name ||
                                  `Attachment 1`
                                }
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  console.error(
                                    "Image load error for:",
                                    announcement.attachments[0].file_url
                                  );
                                  e.target.style.display = "none";
                                }}
                              />
                            )}
                          </div>

                          {/* Additional Images/Files - Show as thumbnails if more than 1 */}
                          {totalAttachments > 1 && (
                            <div className="flex gap-2 overflow-x-auto">
                              {announcement.attachments
                                .slice(1)
                                .map((attachment, index) => (
                                  <div
                                    key={attachment.id || index + 1}
                                    className="relative flex-shrink-0 bg-gray-100 overflow-hidden cursor-pointer hover:opacity-90 transition-all duration-200 border border-gray-200"
                                    style={{
                                      width: "75px",
                                      height: "57.1px",
                                      borderRadius: "16px"
                                    }}
                                    onClick={() =>
                                      isDocument(
                                        attachment.file_name || attachment.name
                                      )
                                        ? handleDocumentClick(attachment)
                                        : handleImageClick(
                                            attachment,
                                            announcement
                                          )
                                    }
                                  >
                                    {isDocument(
                                      attachment.file_name || attachment.name
                                    ) ? (
                                      <div className="w-full h-full flex items-center justify-center">
                                        <div className="scale-75">
                                          {getFileIcon(
                                            attachment.file_name ||
                                              attachment.name
                                          )}
                                        </div>
                                      </div>
                                    ) : (
                                      <img
                                        src={
                                          attachment.file_url ||
                                          attachment.url ||
                                          attachment
                                        }
                                        alt={
                                          attachment.file_name ||
                                          attachment.name ||
                                          `Attachment ${index + 2}`
                                        }
                                        className="w-full h-full object-cover"
                                        onError={(e) => {
                                          console.error(
                                            "Image load error for:",
                                            attachment.file_url
                                          );
                                          e.target.style.display = "none";
                                        }}
                                      />
                                    )}
                                  </div>
                                ))}
                            </div>
                          )}
                        </div>
                      );
                    })()}
                  </div>
                </div>
              )}
          </div>
        );
      })}
    </div>
  );
};

export default AnnouncementListPreview;
