# Generated manually to safely remove title and description fields

from django.db import migrations, connection


def remove_fields_if_exist(apps, schema_editor):
    """
    Safely remove title and description fields if they exist
    """
    with connection.cursor() as cursor:
        # Check if the table exists
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = 'noticeboard_notice'
        """)
        
        if cursor.fetchone()[0] == 0:
            # Table doesn't exist, nothing to do
            return
        
        # Check if title column exists
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'noticeboard_notice' 
            AND column_name = 'title'
        """)
        
        if cursor.fetchone()[0] > 0:
            # Title column exists, drop it
            cursor.execute("ALTER TABLE noticeboard_notice DROP COLUMN title")
        
        # Check if description column exists
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'noticeboard_notice' 
            AND column_name = 'description'
        """)
        
        if cursor.fetchone()[0] > 0:
            # Description column exists, drop it
            cursor.execute("ALTER TABLE noticeboard_notice DROP COLUMN description")


def add_fields_back(apps, schema_editor):
    """
    Reverse migration - add the fields back
    """
    with connection.cursor() as cursor:
        # Add title field back
        cursor.execute("""
            ALTER TABLE noticeboard_notice 
            ADD COLUMN title VARCHAR(255) NULL
        """)
        
        # Add description field back
        cursor.execute("""
            ALTER TABLE noticeboard_notice 
            ADD COLUMN description TEXT NULL
        """)


class Migration(migrations.Migration):

    dependencies = [
        ('noticeboard', '0002_notice_description_notice_title'),
    ]

    operations = [
        migrations.RunPython(remove_fields_if_exist, add_fields_back),
    ]
